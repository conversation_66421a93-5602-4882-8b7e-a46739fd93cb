from sqlalchemy import Column, Date, DateTime, Integer, String, Text, DECIMAL
from sqlalchemy.ext.declarative import declarative_base

Base = declarative_base()


class Movie(Base):
    """电影模型"""

    __tablename__ = "movies"

    id = Column(Integer, primary_key=True, nullable=False)
    douban_id = Column(String(20), nullable=True)
    title = Column(String(500), nullable=True)
    foreign_title = Column(String(500), nullable=True)
    original_title = Column(String(500), nullable=True)
    year = Column(String(12), nullable=True)
    directors = Column(Text, nullable=True)
    writers = Column(Text, nullable=True)
    actors = Column(Text, nullable=True)
    genres = Column(String(500), nullable=True)
    countries = Column(String(500), nullable=True)
    languages = Column(String(500), nullable=True)
    release_date = Column(Date, nullable=True)
    duration = Column(String(100), nullable=True)
    score = Column(DECIMAL(3, 1), nullable=True)
    votes = Column(String(12), nullable=True)
    description = Column(Text, nullable=True)
    cover_url = Column(Text, nullable=True)
    created_at = Column(DateTime, nullable=True)
    updated_at = Column(DateTime, nullable=True)

    def __repr__(self):
        return f"<Movie(id={self.id}, title='{self.title}', year='{self.year}')>"

    def to_dict(self) -> dict:
        """转换为字典"""
        return {
            "id": self.id,
            "douban_id": self.douban_id,
            "title": self.title,
            "foreign_title": self.foreign_title,
            "original_title": self.original_title,
            "year": self.year,
            "directors": self.directors,
            "writers": self.writers,
            "actors": self.actors,
            "genres": self.genres,
            "countries": self.countries,
            "languages": self.languages,
            "release_date": self.release_date.isoformat() if self.release_date else None,
            "duration": self.duration,
            "score": float(self.score) if self.score else None,
            "votes": self.votes,
            "description": self.description,
            "cover_url": self.cover_url,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
        }
